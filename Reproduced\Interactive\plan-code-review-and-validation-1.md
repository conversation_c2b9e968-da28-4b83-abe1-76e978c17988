I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The interactive-agent project has excellent architectural design with comprehensive platform detection, intelligent retry mechanisms, and robust resource management. However, it has critical blocking issues:

1. **Compilation errors** in process-spawn.ts with undefined variables
2. **Missing implementations** for terminal-fallbacks.ts and session-id.ts
3. **Broken test infrastructure** with missing Jest setup and import mismatches
4. **Type definition issues** throughout test files

The codebase shows professional-level planning but needs focused implementation work to become functional.

### Approach

I'll create a comprehensive file-by-file implementation plan to fix the critical issues identified in the code review. The approach focuses on:

- **Fixing compilation errors** in process-spawn.ts and other core files
- **Implementing missing modules** like terminal-fallbacks.ts and session-id.ts  
- **Correcting test infrastructure** with proper Jest setup and mocks
- **Ensuring MCP server functionality** works end-to-end
- **Validating cross-platform compatibility** 

The plan prioritizes critical blocking issues first, then addresses test infrastructure, and finally ensures production readiness through comprehensive validation.

### Reasoning

I conducted a comprehensive code review of the interactive-agent project by examining the package.json, main source files, platform detection logic, utility modules, and test files. I also ran TypeScript diagnostics to identify compilation errors and analyzed the overall architecture. This revealed excellent design patterns but critical implementation gaps that prevent the code from functioning properly.

## Mermaid Diagram

sequenceDiagram
    participant Dev as Developer
    participant Build as Build System
    participant Tests as Test Suite
    participant MCP as MCP Server
    participant Platform as Platform Detection
    participant Spawn as Process Spawning

    Note over Dev,Spawn: Phase 1: Critical Fixes
    Dev->>Build: Fix session-id.ts (NEW)
    Dev->>Build: Fix terminal-fallbacks.ts (NEW)
    Dev->>Build: Fix process-spawn.ts compilation errors
    Dev->>Build: Fix platform-detector.ts issues
    Build->>Build: Resolve all TypeScript errors
    
    Note over Dev,Spawn: Phase 2: Test Infrastructure
    Dev->>Tests: Create jest.setup.js (NEW)
    Dev->>Tests: Fix platform-detector.test.ts
    Dev->>Tests: Fix temp-file-manager.test.ts
    Dev->>Tests: Create retry-manager.test.ts (NEW)
    Dev->>Tests: Create orphan-manager.test.ts (NEW)
    Dev->>Tests: Fix input.test.ts
    Dev->>Tests: Fix process-spawn.test.ts
    Tests->>Tests: All tests pass
    
    Note over Dev,Spawn: Phase 3: Integration & Validation
    Dev->>Build: Fix tools/types.ts
    Dev->>Build: Enhance index.ts (MCP server)
    Dev->>Build: Fix test-platform.js
    Dev->>Build: Enhance build-all.js
    Dev->>Build: Enhance validate-platform.js
    
    Note over Dev,Spawn: Phase 4: End-to-End Testing
    Build->>Platform: Platform detection works
    Platform->>Spawn: Process spawning works
    Spawn->>MCP: MCP server functional
    MCP->>Dev: Production ready
    
    Note over Dev,Spawn: Critical path: session-id → terminal-fallbacks → process-spawn → tests

## Proposed File Changes

### interactive-agent\src\utils\session-id.ts(MODIFY)

References: 

- interactive-agent\src\constants.ts
- interactive-agent\src\utils\temp-file-manager.ts

Create the missing session ID utility module that's referenced throughout the codebase. Implement:

- **generateSessionId()** function using crypto.randomBytes for secure random IDs
- **validateSessionId()** function to check ID format and length
- **SESSION_ID_REGEX** constant for validation pattern
- **isValidSessionId()** helper function
- Use the constants from `../constants.js` for SESSION_ID_BYTE_LENGTH and SESSION_ID_HEX_LENGTH
- Proper error handling for crypto operations
- TypeScript types for session ID operations
- Export all functions and constants for use by other modules

This module is critical as it's imported by temp-file-manager.ts, process-spawn.ts, and other core modules.

### interactive-agent\src\platform\terminal-fallbacks.ts(MODIFY)

References: 

- interactive-agent\src\platform\platform-detector.ts
- interactive-agent\src\utils\retry-manager.ts
- interactive-agent\src\constants.ts

Implement the comprehensive terminal fallback system that's referenced throughout the platform module. Create:

- **TerminalInfo interface** with name, path, available, priority properties
- **EnvironmentInfo interface** for environment detection results
- **TerminalFallbacks class** with static methods:
  - `isCommandAvailable(command: string)` - test if command exists in PATH
  - `canSpawnTerminal()` - check if terminal spawning is possible
  - `findBestTerminal()` - find highest priority available terminal
  - `getAvailableTerminals()` - list all available terminals with status
  - `getFallbackStrategy()` - determine fallback approach (console/terminal)
  - `testTerminalCapability(terminal: string)` - test specific terminal
- **Platform-specific terminal detection** for Windows, macOS, Linux
- **Environment detection** for SSH, Docker, CI/CD, headless environments
- **Graceful fallback logic** when GUI terminals unavailable
- **Integration with retry system** for robust terminal testing
- **Caching of terminal availability** to avoid repeated expensive checks

This module is essential for the platform detection and process spawning functionality.

### interactive-agent\src\platform\process-spawn.ts(MODIFY)

References: 

- interactive-agent\src\utils\retry-manager.ts
- interactive-agent\src\utils\orphan-manager.ts
- interactive-agent\src\constants.ts

Fix the critical compilation errors in the process spawning module. Address:

- **Fix undefined variables**: Declare `spawnTimeout`, `abortController`, and `timeoutId` properly
- **Correct the spawnWithRetry function**: Fix the broken retry logic around lines 384-396
- **Add missing variable declarations**:
  - `const spawnTimeout = options.spawnTimeout || SPAWN_START_TIMEOUT_MS`
  - `const abortController = new AbortController()`
  - Proper timeout handling with `setTimeout`
- **Fix the retry logic flow**: Ensure the RetryManager.executeWithRetry call is properly structured
- **Correct the waitForSpawnEvent call**: Pass the right parameters (spawnTimeout, abortSignal)
- **Fix orphan manager registration**: Ensure it happens after successful spawn
- **Add proper error handling**: Wrap operations in try-catch blocks
- **Ensure all imports are correct**: Verify all referenced modules exist
- **Add missing timeout cleanup**: Clear timeouts in finally blocks

This is the most critical fix as process spawning is core functionality.